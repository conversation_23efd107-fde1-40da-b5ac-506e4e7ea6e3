/**
 * Group Formation Hook
 *
 * React hook for managing group formation, buddy matching,
 * and real-time group suggestions.
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  groupFormationService, 
  type GroupSuggestion, 
  type GroupMember, 
  type GroupFormationRequest 
} from '@/lib/services/groupFormationService';
import { useOptimizedRealtime } from '@/hooks/realtime/useOptimizedRealtime';
import { useAuth } from '@/providers/ConsolidatedAuthProvider';

interface UseGroupFormationOptions {
  enabled?: boolean;
  autoRefresh?: boolean;
  filters?: {
    formation_type?: GroupSuggestion['formation_type'];
    festival_id?: string;
    status?: GroupSuggestion['status'];
  };
}

interface UseGroupFormationResult {
  suggestions: GroupSuggestion[];
  userGroups: GroupMember[];
  isLoading: boolean;
  error: Error | null;
  createGroup: (request: GroupFormationRequest) => Promise<GroupSuggestion>;
  joinGroup: (suggestionId: string) => Promise<void>;
  leaveGroup: (suggestionId: string) => Promise<void>;
  refreshSuggestions: () => Promise<void>;
  getGroupMembers: (suggestionId: string) => Promise<GroupMember[]>;
}

export function useGroupFormation(
  options: UseGroupFormationOptions = {}
): UseGroupFormationResult {
  const {
    enabled = true,
    autoRefresh = true,
    filters = {}
  } = options;

  const { user } = useAuth();
  const [suggestions, setSuggestions] = useState<GroupSuggestion[]>([]);
  const [userGroups, setUserGroups] = useState<GroupMember[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Memoize filters to prevent unnecessary refetches
  const memoizedFilters = useMemo(() => filters, [
    filters.formation_type,
    filters.festival_id,
    filters.status
  ]);

  // Real-time subscription for group suggestions
  useOptimizedRealtime('group_suggestions', ['groups', 'suggestions'], {
    event: '*',
    priority: 'medium',
    callback: (payload) => {
      console.log('🔄 Group Formation: Real-time suggestion update');
      if (enabled && autoRefresh) {
        refreshSuggestions();
      }
    }
  });

  // Real-time subscription for group members
  useOptimizedRealtime('group_members', ['groups', 'members'], {
    event: '*',
    priority: 'medium',
    callback: (payload) => {
      console.log('🔄 Group Formation: Real-time member update');
      if (enabled && autoRefresh) {
        fetchUserGroups();
      }
    }
  });

  // Fetch group suggestions
  const fetchSuggestions = useCallback(async () => {
    if (!enabled || !user) return;

    try {
      setIsLoading(true);
      setError(null);

      console.log('👥 Group Formation Hook: Fetching suggestions');

      const data = await groupFormationService.getGroupSuggestions(
        user.id,
        memoizedFilters
      );

      setSuggestions(data);
      console.log('✅ Group Formation Hook: Fetched', data.length, 'suggestions');
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch group suggestions');
      setError(error);
      console.error('❌ Group Formation Hook: Error fetching suggestions:', error);
    } finally {
      setIsLoading(false);
    }
  }, [enabled, user, memoizedFilters]);

  // Fetch user's groups
  const fetchUserGroups = useCallback(async () => {
    if (!enabled || !user) return;

    try {
      console.log('👥 Group Formation Hook: Fetching user groups');

      const data = await groupFormationService.getUserGroups(user.id);
      setUserGroups(data);
      console.log('✅ Group Formation Hook: Fetched', data.length, 'user groups');
    } catch (err) {
      console.error('❌ Group Formation Hook: Error fetching user groups:', err);
    }
  }, [enabled, user]);

  // Create a new group
  const createGroup = useCallback(async (request: GroupFormationRequest): Promise<GroupSuggestion> => {
    if (!user) {
      throw new Error('User must be authenticated to create a group');
    }

    try {
      console.log('👥 Group Formation Hook: Creating group');

      const suggestion = await groupFormationService.createGroupSuggestion(request, user.id);
      
      // Refresh suggestions to include the new one
      await fetchSuggestions();
      
      console.log('✅ Group Formation Hook: Group created successfully');
      return suggestion;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to create group');
      setError(error);
      console.error('❌ Group Formation Hook: Error creating group:', error);
      throw error;
    }
  }, [user, fetchSuggestions]);

  // Join a group
  const joinGroup = useCallback(async (suggestionId: string): Promise<void> => {
    if (!user) {
      throw new Error('User must be authenticated to join a group');
    }

    try {
      console.log('👥 Group Formation Hook: Joining group', suggestionId);

      await groupFormationService.joinGroup(suggestionId, user.id);
      
      // Refresh both suggestions and user groups
      await Promise.all([fetchSuggestions(), fetchUserGroups()]);
      
      console.log('✅ Group Formation Hook: Joined group successfully');
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to join group');
      setError(error);
      console.error('❌ Group Formation Hook: Error joining group:', error);
      throw error;
    }
  }, [user, fetchSuggestions, fetchUserGroups]);

  // Leave a group
  const leaveGroup = useCallback(async (suggestionId: string): Promise<void> => {
    if (!user) {
      throw new Error('User must be authenticated to leave a group');
    }

    try {
      console.log('👥 Group Formation Hook: Leaving group', suggestionId);

      await groupFormationService.leaveGroup(suggestionId, user.id);
      
      // Refresh both suggestions and user groups
      await Promise.all([fetchSuggestions(), fetchUserGroups()]);
      
      console.log('✅ Group Formation Hook: Left group successfully');
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to leave group');
      setError(error);
      console.error('❌ Group Formation Hook: Error leaving group:', error);
      throw error;
    }
  }, [user, fetchSuggestions, fetchUserGroups]);

  // Get group members
  const getGroupMembers = useCallback(async (suggestionId: string): Promise<GroupMember[]> => {
    try {
      console.log('👥 Group Formation Hook: Fetching group members');

      const members = await groupFormationService.getGroupMembers(suggestionId);
      console.log('✅ Group Formation Hook: Fetched', members.length, 'members');
      
      return members;
    } catch (err) {
      console.error('❌ Group Formation Hook: Error fetching members:', err);
      return [];
    }
  }, []);

  // Refresh suggestions
  const refreshSuggestions = useCallback(async () => {
    await fetchSuggestions();
  }, [fetchSuggestions]);

  // Initial fetch
  useEffect(() => {
    if (enabled && user) {
      Promise.all([fetchSuggestions(), fetchUserGroups()]);
    }
  }, [enabled, user, fetchSuggestions, fetchUserGroups]);

  return {
    suggestions,
    userGroups,
    isLoading,
    error,
    createGroup,
    joinGroup,
    leaveGroup,
    refreshSuggestions,
    getGroupMembers
  };
}

/**
 * Hook for getting group suggestions by type
 */
export function useGroupSuggestionsByType(
  formationType: GroupSuggestion['formation_type'],
  options: Omit<UseGroupFormationOptions, 'filters'> = {}
) {
  return useGroupFormation({
    ...options,
    filters: { formation_type: formationType }
  });
}

/**
 * Hook for getting festival-specific group suggestions
 */
export function useFestivalGroupSuggestions(
  festivalId: string,
  options: Omit<UseGroupFormationOptions, 'filters'> = {}
) {
  return useGroupFormation({
    ...options,
    filters: { festival_id: festivalId }
  });
}

/**
 * Hook for managing a specific group
 */
export function useGroupDetails(suggestionId: string) {
  const [members, setMembers] = useState<GroupMember[]>([]);
  const [suggestion, setSuggestion] = useState<GroupSuggestion | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { getGroupMembers } = useGroupFormation({ enabled: false });

  // Real-time subscription for this specific group
  useOptimizedRealtime('group_members', ['group', suggestionId], {
    event: '*',
    filter: `group_id=eq.${suggestionId}`,
    priority: 'high',
    callback: (payload) => {
      console.log('🔄 Group Details: Real-time member update for group', suggestionId);
      fetchMembers();
    }
  });

  const fetchMembers = useCallback(async () => {
    if (!suggestionId) return;

    try {
      setIsLoading(true);
      const data = await getGroupMembers(suggestionId);
      setMembers(data);
    } catch (error) {
      console.error('❌ Group Details: Error fetching members:', error);
    } finally {
      setIsLoading(false);
    }
  }, [suggestionId, getGroupMembers]);

  useEffect(() => {
    if (suggestionId) {
      fetchMembers();
    }
  }, [suggestionId, fetchMembers]);

  return {
    members,
    suggestion,
    isLoading,
    refreshMembers: fetchMembers
  };
}

export default useGroupFormation;
