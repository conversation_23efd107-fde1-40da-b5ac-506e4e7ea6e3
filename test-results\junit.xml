<testsuites id="" name="" tests="6" failures="6" skipped="0" errors="0" time="21.189351">
<testsuite name="production-validation.spec.js" timestamp="2025-07-02T23:20:52.714Z" hostname="chromium" tests="1" failures="1" skipped="0" time="3.973" errors="0">
<testcase name="Production Readiness Validation › Home page loads successfully with SmartHome component" classname="production-validation.spec.js" time="3.973">
<failure message="production-validation.spec.js:41:3 Home page loads successfully with SmartHome component" type="FAILURE">
<![CDATA[  [chromium] › production-validation.spec.js:41:3 › Production Readiness Validation › Home page loads successfully with SmartHome component 

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5175/
    Call log:
      - navigating to "http://localhost:5175/", waiting until "load"


      40 |
      41 |   test('Home page loads successfully with SmartHome component', async ({ page }) => {
    > 42 |     await page.goto(BASE_URL);
         |                ^
      43 |     await waitForPageLoad(page);
      44 |     
      45 |     // Verify page loads without errors
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\production-validation.spec.js:42:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-chromium\video.webm]]

[[ATTACHMENT|artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="production-validation.spec.js" timestamp="2025-07-02T23:20:52.714Z" hostname="firefox" tests="1" failures="1" skipped="0" time="0.05" errors="0">
<testcase name="Production Readiness Validation › Home page loads successfully with SmartHome component" classname="production-validation.spec.js" time="0.05">
<failure message="production-validation.spec.js:41:3 Home page loads successfully with SmartHome component" type="FAILURE">
<![CDATA[  [firefox] › production-validation.spec.js:41:3 › Production Readiness Validation › Home page loads successfully with SmartHome component 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-firefox\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="production-validation.spec.js" timestamp="2025-07-02T23:20:52.714Z" hostname="webkit" tests="1" failures="1" skipped="0" time="9.482" errors="0">
<testcase name="Production Readiness Validation › Home page loads successfully with SmartHome component" classname="production-validation.spec.js" time="9.482">
<failure message="production-validation.spec.js:41:3 Home page loads successfully with SmartHome component" type="FAILURE">
<![CDATA[  [webkit] › production-validation.spec.js:41:3 › Production Readiness Validation › Home page loads successfully with SmartHome component 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="production-validation.spec.js" timestamp="2025-07-02T23:20:52.714Z" hostname="Mobile Chrome" tests="1" failures="1" skipped="0" time="3.663" errors="0">
<testcase name="Production Readiness Validation › Home page loads successfully with SmartHome component" classname="production-validation.spec.js" time="3.663">
<failure message="production-validation.spec.js:41:3 Home page loads successfully with SmartHome component" type="FAILURE">
<![CDATA[  [Mobile Chrome] › production-validation.spec.js:41:3 › Production Readiness Validation › Home page loads successfully with SmartHome component 

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5175/
    Call log:
      - navigating to "http://localhost:5175/", waiting until "load"


      40 |
      41 |   test('Home page loads successfully with SmartHome component', async ({ page }) => {
    > 42 |     await page.goto(BASE_URL);
         |                ^
      43 |     await waitForPageLoad(page);
      44 |     
      45 |     // Verify page loads without errors
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\production-validation.spec.js:42:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Mobile-Chrome\video.webm]]

[[ATTACHMENT|artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="production-validation.spec.js" timestamp="2025-07-02T23:20:52.714Z" hostname="Mobile Safari" tests="1" failures="1" skipped="0" time="10.138" errors="0">
<testcase name="Production Readiness Validation › Home page loads successfully with SmartHome component" classname="production-validation.spec.js" time="10.138">
<failure message="production-validation.spec.js:41:3 Home page loads successfully with SmartHome component" type="FAILURE">
<![CDATA[  [Mobile Safari] › production-validation.spec.js:41:3 › Production Readiness Validation › Home page loads successfully with SmartHome component 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="production-validation.spec.js" timestamp="2025-07-02T23:20:52.714Z" hostname="Tablet" tests="1" failures="1" skipped="0" time="4.205" errors="0">
<testcase name="Production Readiness Validation › Home page loads successfully with SmartHome component" classname="production-validation.spec.js" time="4.205">
<failure message="production-validation.spec.js:41:3 Home page loads successfully with SmartHome component" type="FAILURE">
<![CDATA[  [Tablet] › production-validation.spec.js:41:3 › Production Readiness Validation › Home page loads successfully with SmartHome component 

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5175/
    Call log:
      - navigating to "http://localhost:5175/", waiting until "load"


      40 |
      41 |   test('Home page loads successfully with SmartHome component', async ({ page }) => {
    > 42 |     await page.goto(BASE_URL);
         |                ^
      43 |     await waitForPageLoad(page);
      44 |     
      45 |     // Verify page loads without errors
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\production-validation.spec.js:42:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Tablet\test-failed-1.png]]

[[ATTACHMENT|artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Tablet\video.webm]]

[[ATTACHMENT|artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Tablet\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>