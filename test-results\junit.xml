<testsuites id="" name="" tests="6" failures="6" skipped="0" errors="0" time="49.882353">
<testsuite name="production-validation.spec.js" timestamp="2025-07-02T22:49:35.072Z" hostname="chromium" tests="1" failures="1" skipped="0" time="25.896" errors="0">
<testcase name="Production Readiness Validation › Home page loads successfully with SmartHome component" classname="production-validation.spec.js" time="25.896">
<failure message="production-validation.spec.js:41:3 Home page loads successfully with SmartHome component" type="FAILURE">
<![CDATA[  [chromium] › production-validation.spec.js:41:3 › Production Readiness Validation › Home page loads successfully with SmartHome component 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.

      12 | // Helper functions
      13 | async function waitForPageLoad(page) {
    > 14 |   await page.waitForLoadState('networkidle');
         |              ^
      15 |   await page.waitForTimeout(1000);
      16 | }
      17 |
        at waitForPageLoad (C:\Users\<USER>\CascadeProjects\festival-family\tests\production-validation.spec.js:14:14)
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\production-validation.spec.js:43:11

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-chromium\video.webm]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="production-validation.spec.js" timestamp="2025-07-02T22:49:35.072Z" hostname="firefox" tests="1" failures="1" skipped="0" time="0.043" errors="0">
<testcase name="Production Readiness Validation › Home page loads successfully with SmartHome component" classname="production-validation.spec.js" time="0.043">
<failure message="production-validation.spec.js:41:3 Home page loads successfully with SmartHome component" type="FAILURE">
<![CDATA[  [firefox] › production-validation.spec.js:41:3 › Production Readiness Validation › Home page loads successfully with SmartHome component 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-firefox\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="production-validation.spec.js" timestamp="2025-07-02T22:49:35.072Z" hostname="webkit" tests="1" failures="1" skipped="0" time="13.678" errors="0">
<testcase name="Production Readiness Validation › Home page loads successfully with SmartHome component" classname="production-validation.spec.js" time="13.678">
<failure message="production-validation.spec.js:41:3 Home page loads successfully with SmartHome component" type="FAILURE">
<![CDATA[  [webkit] › production-validation.spec.js:41:3 › Production Readiness Validation › Home page loads successfully with SmartHome component 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="production-validation.spec.js" timestamp="2025-07-02T22:49:35.072Z" hostname="Mobile Chrome" tests="1" failures="1" skipped="0" time="25.552" errors="0">
<testcase name="Production Readiness Validation › Home page loads successfully with SmartHome component" classname="production-validation.spec.js" time="25.552">
<failure message="production-validation.spec.js:41:3 Home page loads successfully with SmartHome component" type="FAILURE">
<![CDATA[  [Mobile Chrome] › production-validation.spec.js:41:3 › Production Readiness Validation › Home page loads successfully with SmartHome component 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.

      12 | // Helper functions
      13 | async function waitForPageLoad(page) {
    > 14 |   await page.waitForLoadState('networkidle');
         |              ^
      15 |   await page.waitForTimeout(1000);
      16 | }
      17 |
        at waitForPageLoad (C:\Users\<USER>\CascadeProjects\festival-family\tests\production-validation.spec.js:14:14)
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\production-validation.spec.js:43:11

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Mobile-Chrome\video.webm]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="production-validation.spec.js" timestamp="2025-07-02T22:49:35.072Z" hostname="Mobile Safari" tests="1" failures="1" skipped="0" time="11.995" errors="0">
<testcase name="Production Readiness Validation › Home page loads successfully with SmartHome component" classname="production-validation.spec.js" time="11.995">
<failure message="production-validation.spec.js:41:3 Home page loads successfully with SmartHome component" type="FAILURE">
<![CDATA[  [Mobile Safari] › production-validation.spec.js:41:3 › Production Readiness Validation › Home page loads successfully with SmartHome component 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="production-validation.spec.js" timestamp="2025-07-02T22:49:35.072Z" hostname="Tablet" tests="1" failures="1" skipped="0" time="25.544" errors="0">
<testcase name="Production Readiness Validation › Home page loads successfully with SmartHome component" classname="production-validation.spec.js" time="25.544">
<failure message="production-validation.spec.js:41:3 Home page loads successfully with SmartHome component" type="FAILURE">
<![CDATA[  [Tablet] › production-validation.spec.js:41:3 › Production Readiness Validation › Home page loads successfully with SmartHome component 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.

      12 | // Helper functions
      13 | async function waitForPageLoad(page) {
    > 14 |   await page.waitForLoadState('networkidle');
         |              ^
      15 |   await page.waitForTimeout(1000);
      16 | }
      17 |
        at waitForPageLoad (C:\Users\<USER>\CascadeProjects\festival-family\tests\production-validation.spec.js:14:14)
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\production-validation.spec.js:43:11

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Tablet\test-failed-1.png]]

[[ATTACHMENT|artifacts\production-validation-Prod-2025e-ly-with-SmartHome-component-Tablet\video.webm]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>