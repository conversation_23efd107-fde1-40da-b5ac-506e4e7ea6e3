/**
 * Real-time Group Suggestions Hook
 *
 * React hook for managing intelligent real-time group suggestions
 * with automatic updates and user interaction tracking.
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  realtimeGroupSuggestionsService, 
  type UserSuggestion, 
  type SuggestionContext 
} from '@/lib/services/realtimeGroupSuggestionsService';
import { useOptimizedRealtime } from '@/hooks/realtime/useOptimizedRealtime';
import { useAuth } from '@/providers/ConsolidatedAuthProvider';

interface UseRealtimeGroupSuggestionsOptions {
  enabled?: boolean;
  autoRefresh?: boolean;
  suggestionType?: UserSuggestion['suggestion_type'];
  maxSuggestions?: number;
  context?: Partial<SuggestionContext>;
}

interface UseRealtimeGroupSuggestionsResult {
  suggestions: UserSuggestion[];
  newSuggestions: UserSuggestion[];
  isLoading: boolean;
  error: Error | null;
  refreshSuggestions: () => Promise<void>;
  markViewed: (suggestionId: string) => Promise<void>;
  markActedUpon: (suggestionId: string) => Promise<void>;
  generateNewSuggestions: () => Promise<void>;
  hasUnviewedSuggestions: boolean;
  suggestionCount: number;
}

export function useRealtimeGroupSuggestions(
  options: UseRealtimeGroupSuggestionsOptions = {}
): UseRealtimeGroupSuggestionsResult {
  const {
    enabled = true,
    autoRefresh = true,
    suggestionType,
    maxSuggestions = 10,
    context = {}
  } = options;

  const { user } = useAuth();
  const [suggestions, setSuggestions] = useState<UserSuggestion[]>([]);
  const [newSuggestions, setNewSuggestions] = useState<UserSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Memoize context to prevent unnecessary refetches
  const memoizedContext = useMemo(() => context, [
    context.current_activity,
    context.current_location,
    context.festival_id,
    JSON.stringify(context.recent_interactions),
    JSON.stringify(context.preferences)
  ]);

  // Real-time subscription for user suggestions
  useOptimizedRealtime('user_suggestions', ['suggestions', 'realtime'], {
    event: '*',
    filter: user ? `user_id=eq.${user.id}` : undefined,
    priority: 'high',
    callback: (payload) => {
      console.log('🔄 Real-time Suggestions: Update received');
      if (enabled && autoRefresh) {
        fetchSuggestions();
      }
    }
  });

  // Real-time subscription for group suggestions (to trigger new suggestions)
  useOptimizedRealtime('group_suggestions', ['suggestions', 'groups'], {
    event: 'INSERT',
    priority: 'medium',
    callback: (payload) => {
      console.log('🔄 Real-time Suggestions: New group created, triggering suggestions');
      if (enabled && user) {
        triggerSuggestionGeneration();
      }
    }
  });

  // Fetch suggestions
  const fetchSuggestions = useCallback(async () => {
    if (!enabled || !user) return;

    try {
      setIsLoading(true);
      setError(null);

      console.log('🤖 Real-time Suggestions Hook: Fetching suggestions');

      const data = await realtimeGroupSuggestionsService.getUserSuggestions(
        user.id,
        {
          suggestion_type: suggestionType,
          limit: maxSuggestions
        }
      );

      // Separate new (unviewed) suggestions
      const unviewed = data.filter(s => !s.viewed);
      const viewed = data.filter(s => s.viewed);

      setSuggestions(data);
      setNewSuggestions(unviewed);

      console.log('✅ Real-time Suggestions Hook: Fetched', data.length, 'suggestions,', unviewed.length, 'new');
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch suggestions');
      setError(error);
      console.error('❌ Real-time Suggestions Hook: Error fetching suggestions:', error);
    } finally {
      setIsLoading(false);
    }
  }, [enabled, user, suggestionType, maxSuggestions]);

  // Generate new suggestions
  const generateNewSuggestions = useCallback(async () => {
    if (!enabled || !user) return;

    try {
      setIsLoading(true);
      console.log('🤖 Real-time Suggestions Hook: Generating new suggestions');

      const fullContext: SuggestionContext = {
        user_id: user.id,
        recent_interactions: [],
        preferences: {},
        ...memoizedContext
      };

      await realtimeGroupSuggestionsService.generateSuggestions(fullContext);
      
      // Refresh suggestions after generation
      await fetchSuggestions();

      console.log('✅ Real-time Suggestions Hook: Generated new suggestions');
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to generate suggestions');
      setError(error);
      console.error('❌ Real-time Suggestions Hook: Error generating suggestions:', error);
    } finally {
      setIsLoading(false);
    }
  }, [enabled, user, memoizedContext, fetchSuggestions]);

  // Trigger suggestion generation (background)
  const triggerSuggestionGeneration = useCallback(async () => {
    if (!enabled || !user) return;

    try {
      console.log('🤖 Real-time Suggestions Hook: Triggering background generation');

      await realtimeGroupSuggestionsService.triggerSuggestionGeneration(
        user.id,
        memoizedContext
      );

      // Refresh suggestions after a delay
      setTimeout(() => {
        fetchSuggestions();
      }, 2000);
    } catch (err) {
      console.error('❌ Real-time Suggestions Hook: Error triggering generation:', err);
    }
  }, [enabled, user, memoizedContext, fetchSuggestions]);

  // Mark suggestion as viewed
  const markViewed = useCallback(async (suggestionId: string) => {
    try {
      console.log('🤖 Real-time Suggestions Hook: Marking suggestion as viewed');

      await realtimeGroupSuggestionsService.markSuggestionViewed(suggestionId);
      
      // Update local state
      setSuggestions(prev => 
        prev.map(s => s.id === suggestionId ? { ...s, viewed: true } : s)
      );
      setNewSuggestions(prev => 
        prev.filter(s => s.id !== suggestionId)
      );

      console.log('✅ Real-time Suggestions Hook: Marked as viewed');
    } catch (err) {
      console.error('❌ Real-time Suggestions Hook: Error marking viewed:', err);
    }
  }, []);

  // Mark suggestion as acted upon
  const markActedUpon = useCallback(async (suggestionId: string) => {
    try {
      console.log('🤖 Real-time Suggestions Hook: Marking suggestion as acted upon');

      await realtimeGroupSuggestionsService.markSuggestionActedUpon(suggestionId);
      
      // Update local state
      setSuggestions(prev => 
        prev.map(s => s.id === suggestionId ? { ...s, acted_upon: true, viewed: true } : s)
      );
      setNewSuggestions(prev => 
        prev.filter(s => s.id !== suggestionId)
      );

      console.log('✅ Real-time Suggestions Hook: Marked as acted upon');
    } catch (err) {
      console.error('❌ Real-time Suggestions Hook: Error marking acted upon:', err);
    }
  }, []);

  // Refresh suggestions
  const refreshSuggestions = useCallback(async () => {
    await fetchSuggestions();
  }, [fetchSuggestions]);

  // Computed values
  const hasUnviewedSuggestions = newSuggestions.length > 0;
  const suggestionCount = suggestions.length;

  // Initial fetch and periodic generation
  useEffect(() => {
    if (enabled && user) {
      fetchSuggestions();
      
      // Generate suggestions on mount if none exist
      const generateInitial = async () => {
        const existing = await realtimeGroupSuggestionsService.getUserSuggestions(
          user.id,
          { limit: 1 }
        );
        
        if (existing.length === 0) {
          await generateNewSuggestions();
        }
      };
      
      generateInitial();
    }
  }, [enabled, user, fetchSuggestions, generateNewSuggestions]);

  // Auto-generate suggestions based on user activity
  useEffect(() => {
    if (enabled && user && memoizedContext.current_activity) {
      // Trigger suggestion generation when user activity changes
      const timer = setTimeout(() => {
        triggerSuggestionGeneration();
      }, 5000); // 5 second delay to avoid too frequent generation

      return () => clearTimeout(timer);
    }
  }, [enabled, user, memoizedContext.current_activity, triggerSuggestionGeneration]);

  return {
    suggestions,
    newSuggestions,
    isLoading,
    error,
    refreshSuggestions,
    markViewed,
    markActedUpon,
    generateNewSuggestions,
    hasUnviewedSuggestions,
    suggestionCount
  };
}

/**
 * Hook for getting suggestions by type
 */
export function useRealtimeGroupSuggestionsByType(
  suggestionType: UserSuggestion['suggestion_type'],
  options: Omit<UseRealtimeGroupSuggestionsOptions, 'suggestionType'> = {}
) {
  return useRealtimeGroupSuggestions({
    ...options,
    suggestionType
  });
}

/**
 * Hook for buddy match suggestions
 */
export function useBuddyMatchSuggestions(
  options: Omit<UseRealtimeGroupSuggestionsOptions, 'suggestionType'> = {}
) {
  return useRealtimeGroupSuggestions({
    ...options,
    suggestionType: 'buddy_match'
  });
}

/**
 * Hook for group join suggestions
 */
export function useGroupJoinSuggestions(
  options: Omit<UseRealtimeGroupSuggestionsOptions, 'suggestionType'> = {}
) {
  return useRealtimeGroupSuggestions({
    ...options,
    suggestionType: 'group_join'
  });
}

/**
 * Hook for activity-based suggestions with context
 */
export function useActivityBasedSuggestions(
  activityId: string,
  options: Omit<UseRealtimeGroupSuggestionsOptions, 'context'> = {}
) {
  return useRealtimeGroupSuggestions({
    ...options,
    context: {
      current_activity: activityId,
      preferences: {
        activity_types: [activityId]
      }
    }
  });
}

/**
 * Hook for festival-specific suggestions
 */
export function useFestivalSuggestions(
  festivalId: string,
  options: Omit<UseRealtimeGroupSuggestionsOptions, 'context'> = {}
) {
  return useRealtimeGroupSuggestions({
    ...options,
    context: {
      festival_id: festivalId
    }
  });
}

export default useRealtimeGroupSuggestions;
