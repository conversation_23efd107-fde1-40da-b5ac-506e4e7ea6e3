# Test info

- Name: Production Readiness Validation >> Home page loads successfully with SmartHome component
- Location: C:\Users\<USER>\CascadeProjects\festival-family\tests\production-validation.spec.js:41:3

# Error details

```
Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5175/
Call log:
  - navigating to "http://localhost:5175/", waiting until "load"

    at C:\Users\<USER>\CascadeProjects\festival-family\tests\production-validation.spec.js:42:16
```

# Test source

```ts
   1 | /**
   2 |  * Production Validation Tests
   3 |  *
   4 |  * Quick validation tests for production readiness including
   5 |  * performance, real-time functionality, and error handling.
   6 |  */
   7 |
   8 | import { test, expect } from '@playwright/test';
   9 |
   10 | const BASE_URL = 'http://localhost:5175';
   11 |
   12 | // Helper functions
   13 | async function waitForPageLoad(page) {
   14 |   await page.waitForLoadState('networkidle');
   15 |   await page.waitForTimeout(1000);
   16 | }
   17 |
   18 | async function checkPerformanceMetrics(page) {
   19 |   const performanceMetrics = await page.evaluate(() => {
   20 |     return {
   21 |       loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
   22 |       domContentLoaded: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
   23 |       firstPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime || 0,
   24 |       firstContentfulPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-contentful-paint')?.startTime || 0
   25 |     };
   26 |   });
   27 |   
   28 |   return performanceMetrics;
   29 | }
   30 |
   31 | test.describe('Production Readiness Validation', () => {
   32 |   test.beforeEach(async ({ page }) => {
   33 |     // Set up console logging for debugging
   34 |     page.on('console', msg => {
   35 |       if (msg.type() === 'error') {
   36 |         console.error('Browser console error:', msg.text());
   37 |       }
   38 |     });
   39 |   });
   40 |
   41 |   test('Home page loads successfully with SmartHome component', async ({ page }) => {
>  42 |     await page.goto(BASE_URL);
      |                ^ Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5175/
   43 |     await waitForPageLoad(page);
   44 |     
   45 |     // Verify page loads without errors
   46 |     await expect(page).toHaveTitle(/Festival Family/);
   47 |     
   48 |     // Check for critical elements
   49 |     const bodyContent = await page.textContent('body');
   50 |     expect(bodyContent).toBeTruthy();
   51 |     
   52 |     console.log('✅ Home page loads successfully');
   53 |   });
   54 |
   55 |   test('Activities page performance and functionality', async ({ page }) => {
   56 |     await page.goto(`${BASE_URL}/activities`);
   57 |     await waitForPageLoad(page);
   58 |     
   59 |     // Check performance metrics
   60 |     const metrics = await checkPerformanceMetrics(page);
   61 |     expect(metrics.loadTime).toBeLessThan(5000); // Less than 5 seconds
   62 |     
   63 |     // Verify Activities page elements
   64 |     const hasContent = await page.evaluate(() => {
   65 |       return document.body.textContent && document.body.textContent.trim().length > 100;
   66 |     });
   67 |     expect(hasContent).toBeTruthy();
   68 |     
   69 |     console.log('✅ Activities page performance validated:', metrics);
   70 |   });
   71 |
   72 |   test('Real-time subscription functionality', async ({ page }) => {
   73 |     await page.goto(`${BASE_URL}/activities`);
   74 |     await waitForPageLoad(page);
   75 |     
   76 |     // Monitor console for real-time subscription messages
   77 |     const realtimeMessages = [];
   78 |     page.on('console', msg => {
   79 |       const text = msg.text();
   80 |       if (text.includes('Optimized subscription') || text.includes('Real-time update')) {
   81 |         realtimeMessages.push(text);
   82 |       }
   83 |     });
   84 |     
   85 |     // Wait for subscriptions to be established
   86 |     await page.waitForTimeout(5000);
   87 |     
   88 |     // Verify subscription messages (should have at least some activity)
   89 |     console.log('📡 Real-time messages captured:', realtimeMessages.length);
   90 |     
   91 |     console.log('✅ Real-time subscriptions monitoring completed');
   92 |   });
   93 |
   94 |   test('Error boundary functionality', async ({ page }) => {
   95 |     await page.goto(BASE_URL);
   96 |     await waitForPageLoad(page);
   97 |     
   98 |     // Check if error boundary is working (page should still be functional)
   99 |     const bodyContent = await page.textContent('body');
  100 |     expect(bodyContent).toBeTruthy();
  101 |     
  102 |     console.log('✅ Error boundary handling verified');
  103 |   });
  104 |
  105 |   test('Mobile responsiveness', async ({ page }) => {
  106 |     // Test mobile viewport
  107 |     await page.setViewportSize({ width: 375, height: 667 });
  108 |     await page.goto(`${BASE_URL}/activities`);
  109 |     await waitForPageLoad(page);
  110 |     
  111 |     // Verify mobile layout
  112 |     const bodyWidth = await page.evaluate(() => document.body.offsetWidth);
  113 |     expect(bodyWidth).toBeLessThanOrEqual(375);
  114 |     
  115 |     // Test desktop viewport
  116 |     await page.setViewportSize({ width: 1920, height: 1080 });
  117 |     await page.reload();
  118 |     await waitForPageLoad(page);
  119 |     
  120 |     console.log('✅ Mobile responsiveness verified');
  121 |   });
  122 |
  123 |   test('Navigation and routing', async ({ page }) => {
  124 |     await page.goto(BASE_URL);
  125 |     await waitForPageLoad(page);
  126 |     
  127 |     // Test navigation to Activities
  128 |     await page.goto(`${BASE_URL}/activities`);
  129 |     await waitForPageLoad(page);
  130 |     await expect(page).toHaveURL(/activities/);
  131 |     
  132 |     // Test navigation to Discover
  133 |     await page.goto(`${BASE_URL}/discover`);
  134 |     await waitForPageLoad(page);
  135 |     await expect(page).toHaveURL(/discover/);
  136 |     
  137 |     console.log('✅ Navigation and routing verified');
  138 |   });
  139 |
  140 |   test('TypeScript compliance and build integrity', async ({ page }) => {
  141 |     await page.goto(BASE_URL);
  142 |     await waitForPageLoad(page);
```