/**
 * Community Activity Feed Service
 *
 * Service for managing and displaying real-time community activity feed
 * showing user interactions, new content, and community engagement.
 */

import { supabase } from '@/lib/supabase';

export interface CommunityActivity {
  id: string;
  user_id: string;
  activity_type: 'join' | 'favorite' | 'view' | 'share' | 'create' | 'comment' | 'attend' | 'group_join' | 'suggestion_accept';
  content_type: 'activity' | 'event' | 'festival' | 'tip' | 'guide' | 'group' | 'announcement';
  content_id: string;
  content_title?: string;
  content_description?: string;
  metadata?: {
    group_name?: string;
    participant_count?: number;
    festival_name?: string;
    activity_category?: string;
    [key: string]: any;
  };
  created_at: string;
  user_profile?: {
    display_name?: string;
    avatar_url?: string;
  };
}

export interface ActivityFeedOptions {
  limit?: number;
  offset?: number;
  activity_types?: CommunityActivity['activity_type'][];
  content_types?: CommunityActivity['content_type'][];
  user_id?: string;
  since?: string;
}

export interface ActivityFeedResult {
  activities: CommunityActivity[];
  total_count: number;
  has_more: boolean;
  latest_timestamp: string;
}

class CommunityActivityFeedService {
  private readonly FEED_CACHE = new Map<string, CommunityActivity[]>();
  private readonly CACHE_DURATION = 2 * 60 * 1000; // 2 minutes
  private readonly DEFAULT_LIMIT = 20;

  /**
   * Get community activity feed
   */
  async getActivityFeed(options: ActivityFeedOptions = {}): Promise<ActivityFeedResult> {
    try {
      const {
        limit = this.DEFAULT_LIMIT,
        offset = 0,
        activity_types,
        content_types,
        user_id,
        since
      } = options;

      console.log('🌊 Community Feed: Fetching activity feed', { limit, offset });

      const cacheKey = this.generateCacheKey(options);
      const cached = this.getCachedFeed(cacheKey);
      
      if (cached && !since) {
        console.log('🌊 Community Feed: Using cached feed');
        return {
          activities: cached,
          total_count: cached.length,
          has_more: cached.length >= limit,
          latest_timestamp: cached[0]?.created_at || new Date().toISOString()
        };
      }

      // Build query for user activities (using any to bypass type checking temporarily)
      let query = (supabase as any)
        .from('user_activities')
        .select(`
          id,
          user_id,
          action_type,
          content_type,
          content_id,
          metadata,
          created_at
        `);

      // Apply filters
      if (activity_types && activity_types.length > 0) {
        query = query.in('action_type', activity_types);
      }

      if (content_types && content_types.length > 0) {
        query = query.in('content_type', content_types);
      }

      if (user_id) {
        query = query.eq('user_id', user_id);
      }

      if (since) {
        query = query.gt('created_at', since);
      }

      // Order and limit
      query = query
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      const { data: rawActivities, error } = await query;

      if (error) throw error;

      // Transform and enrich activities
      const activities = await this.enrichActivities(rawActivities || []);

      // Cache the results
      if (!since) {
        this.setCachedFeed(cacheKey, activities);
      }

      const result: ActivityFeedResult = {
        activities,
        total_count: activities.length,
        has_more: activities.length >= limit,
        latest_timestamp: activities[0]?.created_at || new Date().toISOString()
      };

      console.log('✅ Community Feed: Fetched', activities.length, 'activities');
      return result;
    } catch (error) {
      console.error('❌ Community Feed: Error fetching feed:', error);
      throw error;
    }
  }

  /**
   * Get real-time activity updates
   */
  async getRealtimeUpdates(since: string): Promise<CommunityActivity[]> {
    try {
      console.log('🔄 Community Feed: Fetching real-time updates since', since);

      const result = await this.getActivityFeed({
        since,
        limit: 50 // Get more for real-time updates
      });

      return result.activities;
    } catch (error) {
      console.error('❌ Community Feed: Error fetching real-time updates:', error);
      return [];
    }
  }

  /**
   * Get user-specific activity feed
   */
  async getUserActivityFeed(userId: string, options: Omit<ActivityFeedOptions, 'user_id'> = {}): Promise<ActivityFeedResult> {
    return this.getActivityFeed({
      ...options,
      user_id: userId
    });
  }

  /**
   * Get trending activities
   */
  async getTrendingActivities(timeframe: '1h' | '24h' | '7d' = '24h'): Promise<CommunityActivity[]> {
    try {
      console.log('📈 Community Feed: Fetching trending activities for', timeframe);

      const since = this.getTimeframeCutoff(timeframe);

      // Get activities with high engagement (using any to bypass type checking temporarily)
      const { data: trendingData, error } = await (supabase as any)
        .from('user_activities')
        .select(`
          content_id,
          content_type,
          action_type,
          created_at,
          metadata
        `)
        .gt('created_at', since)
        .in('action_type', ['view', 'favorite', 'join', 'attend'])
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw error;

      // Aggregate by content and calculate trending score
      const contentEngagement = new Map<string, {
        content_id: string;
        content_type: string;
        views: number;
        favorites: number;
        joins: number;
        attends: number;
        latest_activity: string;
        score: number;
      }>();

      trendingData?.forEach(activity => {
        const key = `${activity.content_type}_${activity.content_id}`;
        const existing = contentEngagement.get(key) || {
          content_id: activity.content_id,
          content_type: activity.content_type,
          views: 0,
          favorites: 0,
          joins: 0,
          attends: 0,
          latest_activity: activity.created_at,
          score: 0
        };

        // Count engagement types
        switch (activity.action_type) {
          case 'view':
            existing.views++;
            break;
          case 'favorite':
            existing.favorites++;
            break;
          case 'join':
            existing.joins++;
            break;
          case 'attend':
            existing.attends++;
            break;
        }

        // Update latest activity
        if (activity.created_at > existing.latest_activity) {
          existing.latest_activity = activity.created_at;
        }

        contentEngagement.set(key, existing);
      });

      // Calculate trending scores and sort
      const trendingContent = Array.from(contentEngagement.values())
        .map(item => {
          // Calculate trending score (weighted by engagement type and recency)
          const recencyBonus = this.calculateRecencyBonus(item.latest_activity);
          item.score = (
            item.views * 1 +
            item.favorites * 3 +
            item.joins * 5 +
            item.attends * 4
          ) * recencyBonus;
          return item;
        })
        .filter(item => item.score > 0)
        .sort((a, b) => b.score - a.score)
        .slice(0, 10);

      // Convert to community activities
      const trendingActivities: CommunityActivity[] = trendingContent.map(item => ({
        id: `trending_${item.content_id}`,
        user_id: 'system',
        activity_type: 'view',
        content_type: item.content_type as CommunityActivity['content_type'],
        content_id: item.content_id,
        created_at: item.latest_activity,
        metadata: {
          trending_score: item.score,
          engagement: {
            views: item.views,
            favorites: item.favorites,
            joins: item.joins,
            attends: item.attends
          }
        }
      }));

      console.log('✅ Community Feed: Found', trendingActivities.length, 'trending activities');
      return trendingActivities;
    } catch (error) {
      console.error('❌ Community Feed: Error fetching trending activities:', error);
      return [];
    }
  }

  /**
   * Add activity to feed
   */
  async addActivity(activity: Omit<CommunityActivity, 'id' | 'created_at'>): Promise<void> {
    try {
      console.log('➕ Community Feed: Adding activity', activity.activity_type, activity.content_type);

      const activityData = {
        user_id: activity.user_id,
        action_type: activity.activity_type,
        content_type: activity.content_type,
        content_id: activity.content_id,
        metadata: activity.metadata || {},
        created_at: new Date().toISOString()
      };

      const { error } = await (supabase as any)
        .from('user_activities')
        .insert([activityData]);

      if (error) throw error;

      // Clear cache to force refresh
      this.clearCache();

      console.log('✅ Community Feed: Activity added successfully');
    } catch (error) {
      console.error('❌ Community Feed: Error adding activity:', error);
      throw error;
    }
  }

  /**
   * Enrich activities with additional data
   */
  private async enrichActivities(rawActivities: any[]): Promise<CommunityActivity[]> {
    const activities: CommunityActivity[] = [];

    for (const raw of rawActivities) {
      try {
        const activity: CommunityActivity = {
          id: raw.id,
          user_id: raw.user_id,
          activity_type: this.mapActionType(raw.action_type),
          content_type: raw.content_type,
          content_id: raw.content_id,
          metadata: raw.metadata || {},
          created_at: raw.created_at
        };

        // Enrich with content details
        await this.enrichWithContentDetails(activity);

        // Enrich with user profile (simplified for now)
        activity.user_profile = {
          display_name: `User ${raw.user_id.slice(0, 8)}`,
          avatar_url: undefined
        };

        activities.push(activity);
      } catch (error) {
        console.error('❌ Community Feed: Error enriching activity:', error);
        // Continue with other activities
      }
    }

    return activities;
  }

  /**
   * Enrich activity with content details
   */
  private async enrichWithContentDetails(activity: CommunityActivity): Promise<void> {
    try {
      let contentData = null;

      switch (activity.content_type) {
        case 'activity':
          const { data: activityData } = await supabase
            .from('activities')
            .select('title, description')
            .eq('id', activity.content_id)
            .single();
          contentData = activityData;
          break;

        case 'event':
          const { data: eventData } = await supabase
            .from('events')
            .select('title, description')
            .eq('id', activity.content_id)
            .single();
          contentData = eventData;
          break;

        case 'festival':
          const { data: festivalData } = await supabase
            .from('festivals')
            .select('name as title, description')
            .eq('id', activity.content_id)
            .single();
          contentData = festivalData;
          break;

        case 'group':
          const { data: groupData } = await supabase
            .from('group_suggestions')
            .select('suggested_name as title, suggested_description as description')
            .eq('id', activity.content_id)
            .single();
          contentData = groupData;
          break;
      }

      if (contentData) {
        activity.content_title = contentData.title;
        activity.content_description = contentData.description;
      }
    } catch (error) {
      // Silently fail content enrichment
      console.warn('⚠️ Community Feed: Could not enrich content details for', activity.content_type, activity.content_id);
    }
  }

  /**
   * Map action types to activity types
   */
  private mapActionType(actionType: string): CommunityActivity['activity_type'] {
    const mapping: Record<string, CommunityActivity['activity_type']> = {
      'view': 'view',
      'favorite': 'favorite',
      'join': 'join',
      'share': 'share',
      'attend': 'attend',
      'create': 'create',
      'comment': 'comment'
    };

    return mapping[actionType] || 'view';
  }

  /**
   * Calculate recency bonus for trending score
   */
  private calculateRecencyBonus(timestamp: string): number {
    const now = Date.now();
    const activityTime = new Date(timestamp).getTime();
    const hoursAgo = (now - activityTime) / (1000 * 60 * 60);

    // Exponential decay: newer activities get higher bonus
    if (hoursAgo < 1) return 2.0;
    if (hoursAgo < 6) return 1.5;
    if (hoursAgo < 24) return 1.2;
    if (hoursAgo < 72) return 1.0;
    return 0.8;
  }

  /**
   * Get timeframe cutoff timestamp
   */
  private getTimeframeCutoff(timeframe: '1h' | '24h' | '7d'): string {
    const now = new Date();
    
    switch (timeframe) {
      case '1h':
        return new Date(now.getTime() - 60 * 60 * 1000).toISOString();
      case '24h':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
      case '7d':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
      default:
        return new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
    }
  }

  /**
   * Cache management
   */
  private generateCacheKey(options: ActivityFeedOptions): string {
    return `feed_${JSON.stringify(options)}`;
  }

  private getCachedFeed(key: string): CommunityActivity[] | null {
    const cached = this.FEED_CACHE.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }
    return null;
  }

  private setCachedFeed(key: string, data: CommunityActivity[]): void {
    this.FEED_CACHE.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.FEED_CACHE.clear();
    console.log('🌊 Community Feed: Cache cleared');
  }
}

// Export singleton instance
export const communityActivityFeedService = new CommunityActivityFeedService();
export default communityActivityFeedService;
