{"config": {"configFile": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\playwright.config.js", "rootDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 6}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report"}], ["json", {"outputFile": "test-results/results.json"}], ["list", null], ["junit", {"outputFile": "test-results/junit.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Tablet", "name": "Tablet", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": {"command": "npm run dev", "port": 5173, "reuseExistingServer": true, "timeout": 120000, "env": {"NODE_ENV": "test"}}}, "suites": [{"title": "production-validation.spec.js", "file": "production-validation.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Production Readiness Validation", "file": "production-validation.spec.js", "line": 31, "column": 6, "specs": [{"title": "Home page loads successfully with SmartHome component", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 3973, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5175/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5175/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5175/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5175/\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\production-validation.spec.js:42:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\production-validation.spec.js", "column": 16, "line": 42}, "snippet": "\u001b[0m \u001b[90m 40 |\u001b[39m\n \u001b[90m 41 |\u001b[39m   test(\u001b[32m'Home page loads successfully with SmartHome component'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 42 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mBASE_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 43 |\u001b[39m     \u001b[36mawait\u001b[39m waitForPageLoad(page)\u001b[33m;\u001b[39m\n \u001b[90m 44 |\u001b[39m     \n \u001b[90m 45 |\u001b[39m     \u001b[90m// Verify page loads without errors\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\production-validation.spec.js", "column": 16, "line": 42}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5175/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5175/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 40 |\u001b[39m\n \u001b[90m 41 |\u001b[39m   test(\u001b[32m'Home page loads successfully with SmartHome component'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 42 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mBASE_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 43 |\u001b[39m     \u001b[36mawait\u001b[39m waitForPageLoad(page)\u001b[33m;\u001b[39m\n \u001b[90m 44 |\u001b[39m     \n \u001b[90m 45 |\u001b[39m     \u001b[90m// Verify page loads without errors\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\production-validation.spec.js:42:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-02T23:20:57.942Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\production-validation-Prod-2025e-ly-with-SmartHome-component-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\production-validation-Prod-2025e-ly-with-SmartHome-component-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\production-validation-Prod-2025e-ly-with-SmartHome-component-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\production-validation.spec.js", "column": 16, "line": 42}}], "status": "unexpected"}], "id": "55ad658ac2acac9f3a47-0486de091115e0cc8d72", "file": "production-validation.spec.js", "line": 41, "column": 3}, {"title": "Home page loads successfully with SmartHome component", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 50, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-02T23:20:57.732Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\production-validation-Prod-2025e-ly-with-SmartHome-component-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "55ad658ac2acac9f3a47-cc9d6580827567982ba1", "file": "production-validation.spec.js", "line": 41, "column": 3}, {"title": "Home page loads successfully with SmartHome component", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 9482, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-02T23:20:58.065Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "55ad658ac2acac9f3a47-96dd9ebfce910b831f0e", "file": "production-validation.spec.js", "line": 41, "column": 3}, {"title": "Home page loads successfully with SmartHome component", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 3663, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5175/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5175/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5175/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5175/\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\production-validation.spec.js:42:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\production-validation.spec.js", "column": 16, "line": 42}, "snippet": "\u001b[0m \u001b[90m 40 |\u001b[39m\n \u001b[90m 41 |\u001b[39m   test(\u001b[32m'Home page loads successfully with SmartHome component'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 42 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mBASE_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 43 |\u001b[39m     \u001b[36mawait\u001b[39m waitForPageLoad(page)\u001b[33m;\u001b[39m\n \u001b[90m 44 |\u001b[39m     \n \u001b[90m 45 |\u001b[39m     \u001b[90m// Verify page loads without errors\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\production-validation.spec.js", "column": 16, "line": 42}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5175/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5175/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 40 |\u001b[39m\n \u001b[90m 41 |\u001b[39m   test(\u001b[32m'Home page loads successfully with SmartHome component'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 42 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mBASE_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 43 |\u001b[39m     \u001b[36mawait\u001b[39m waitForPageLoad(page)\u001b[33m;\u001b[39m\n \u001b[90m 44 |\u001b[39m     \n \u001b[90m 45 |\u001b[39m     \u001b[90m// Verify page loads without errors\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\production-validation.spec.js:42:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-02T23:20:57.409Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\production-validation-Prod-2025e-ly-with-SmartHome-component-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\production-validation-Prod-2025e-ly-with-SmartHome-component-Mobile-Chrome\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\production-validation-Prod-2025e-ly-with-SmartHome-component-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\production-validation.spec.js", "column": 16, "line": 42}}], "status": "unexpected"}], "id": "55ad658ac2acac9f3a47-0cc4d4f534b5711e56b8", "file": "production-validation.spec.js", "line": 41, "column": 3}, {"title": "Home page loads successfully with SmartHome component", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "failed", "duration": 10138, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-02T23:20:57.477Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "55ad658ac2acac9f3a47-edf4fdb0bf40ca2025e8", "file": "production-validation.spec.js", "line": 41, "column": 3}, {"title": "Home page loads successfully with SmartHome component", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "failed", "duration": 4205, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5175/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5175/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5175/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5175/\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\production-validation.spec.js:42:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\production-validation.spec.js", "column": 16, "line": 42}, "snippet": "\u001b[0m \u001b[90m 40 |\u001b[39m\n \u001b[90m 41 |\u001b[39m   test(\u001b[32m'Home page loads successfully with SmartHome component'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 42 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mBASE_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 43 |\u001b[39m     \u001b[36mawait\u001b[39m waitForPageLoad(page)\u001b[33m;\u001b[39m\n \u001b[90m 44 |\u001b[39m     \n \u001b[90m 45 |\u001b[39m     \u001b[90m// Verify page loads without errors\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\production-validation.spec.js", "column": 16, "line": 42}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5175/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5175/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 40 |\u001b[39m\n \u001b[90m 41 |\u001b[39m   test(\u001b[32m'Home page loads successfully with SmartHome component'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 42 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mBASE_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 43 |\u001b[39m     \u001b[36mawait\u001b[39m waitForPageLoad(page)\u001b[33m;\u001b[39m\n \u001b[90m 44 |\u001b[39m     \n \u001b[90m 45 |\u001b[39m     \u001b[90m// Verify page loads without errors\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\production-validation.spec.js:42:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-02T23:20:57.753Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\production-validation-Prod-2025e-ly-with-SmartHome-component-Tablet\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\production-validation-Prod-2025e-ly-with-SmartHome-component-Tablet\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\production-validation-Prod-2025e-ly-with-SmartHome-component-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\production-validation.spec.js", "column": 16, "line": 42}}], "status": "unexpected"}], "id": "55ad658ac2acac9f3a47-fbe8e471edec47714ecb", "file": "production-validation.spec.js", "line": 41, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-02T23:20:48.482Z", "duration": 21189.351, "expected": 0, "skipped": 0, "unexpected": 6, "flaky": 0}}