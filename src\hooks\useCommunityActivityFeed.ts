/**
 * Community Activity Feed Hook
 *
 * React hook for managing community activity feed with real-time updates,
 * infinite scrolling, and trending content.
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  communityActivityFeedService, 
  type CommunityActivity, 
  type ActivityFeedOptions 
} from '@/lib/services/communityActivityFeedService';
import { useOptimizedRealtime } from '@/hooks/realtime/useOptimizedRealtime';
import { useAuth } from '@/providers/ConsolidatedAuthProvider';

interface UseCommunityActivityFeedOptions extends ActivityFeedOptions {
  enabled?: boolean;
  autoRefresh?: boolean;
  realTimeUpdates?: boolean;
  infiniteScroll?: boolean;
}

interface UseCommunityActivityFeedResult {
  activities: CommunityActivity[];
  trendingActivities: CommunityActivity[];
  isLoading: boolean;
  isLoadingMore: boolean;
  error: Error | null;
  hasMore: boolean;
  totalCount: number;
  refreshFeed: () => Promise<void>;
  loadMore: () => Promise<void>;
  addActivity: (activity: Omit<CommunityActivity, 'id' | 'created_at'>) => Promise<void>;
  newActivitiesCount: number;
  loadNewActivities: () => void;
}

export function useCommunityActivityFeed(
  options: UseCommunityActivityFeedOptions = {}
): UseCommunityActivityFeedResult {
  const {
    enabled = true,
    autoRefresh = true,
    realTimeUpdates = true,
    infiniteScroll = true,
    limit = 20,
    activity_types,
    content_types,
    user_id
  } = options;

  const { user } = useAuth();
  const [activities, setActivities] = useState<CommunityActivity[]>([]);
  const [trendingActivities, setTrendingActivities] = useState<CommunityActivity[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [newActivitiesCount, setNewActivitiesCount] = useState(0);
  const [latestTimestamp, setLatestTimestamp] = useState<string>('');

  // Memoize options to prevent unnecessary refetches
  const memoizedOptions = useMemo(() => ({
    limit,
    activity_types,
    content_types,
    user_id
  }), [limit, JSON.stringify(activity_types), JSON.stringify(content_types), user_id]);

  // Real-time subscription for user activities
  useOptimizedRealtime('user_activities', ['community', 'feed'], {
    event: 'INSERT',
    priority: 'medium',
    callback: (payload) => {
      console.log('🔄 Community Feed: Real-time activity received');
      if (enabled && realTimeUpdates) {
        handleRealtimeUpdate();
      }
    }
  });

  // Handle real-time updates
  const handleRealtimeUpdate = useCallback(async () => {
    if (!latestTimestamp) return;

    try {
      const newActivities = await communityActivityFeedService.getRealtimeUpdates(latestTimestamp);
      
      if (newActivities.length > 0) {
        setNewActivitiesCount(prev => prev + newActivities.length);
        console.log('🔄 Community Feed: Found', newActivities.length, 'new activities');
      }
    } catch (error) {
      console.error('❌ Community Feed: Error handling real-time update:', error);
    }
  }, [latestTimestamp]);

  // Fetch initial feed
  const fetchFeed = useCallback(async (reset = true) => {
    if (!enabled) return;

    try {
      setIsLoading(reset);
      setError(null);

      console.log('🌊 Community Feed Hook: Fetching feed');

      const result = await communityActivityFeedService.getActivityFeed({
        ...memoizedOptions,
        offset: reset ? 0 : activities.length
      });

      if (reset) {
        setActivities(result.activities);
        setLatestTimestamp(result.latest_timestamp);
      } else {
        setActivities(prev => [...prev, ...result.activities]);
      }

      setHasMore(result.has_more);
      setTotalCount(result.total_count);

      console.log('✅ Community Feed Hook: Fetched', result.activities.length, 'activities');
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch community feed');
      setError(error);
      console.error('❌ Community Feed Hook: Error fetching feed:', error);
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  }, [enabled, memoizedOptions, activities.length]);

  // Fetch trending activities
  const fetchTrending = useCallback(async () => {
    if (!enabled) return;

    try {
      console.log('📈 Community Feed Hook: Fetching trending activities');

      const trending = await communityActivityFeedService.getTrendingActivities('24h');
      setTrendingActivities(trending);

      console.log('✅ Community Feed Hook: Fetched', trending.length, 'trending activities');
    } catch (error) {
      console.error('❌ Community Feed Hook: Error fetching trending:', error);
    }
  }, [enabled]);

  // Load more activities (infinite scroll)
  const loadMore = useCallback(async () => {
    if (!enabled || !hasMore || isLoadingMore) return;

    setIsLoadingMore(true);
    await fetchFeed(false);
  }, [enabled, hasMore, isLoadingMore, fetchFeed]);

  // Refresh feed
  const refreshFeed = useCallback(async () => {
    setNewActivitiesCount(0);
    await Promise.all([fetchFeed(true), fetchTrending()]);
  }, [fetchFeed, fetchTrending]);

  // Load new activities
  const loadNewActivities = useCallback(() => {
    setNewActivitiesCount(0);
    refreshFeed();
  }, [refreshFeed]);

  // Add new activity
  const addActivity = useCallback(async (activity: Omit<CommunityActivity, 'id' | 'created_at'>) => {
    try {
      console.log('➕ Community Feed Hook: Adding activity');

      await communityActivityFeedService.addActivity(activity);
      
      // Refresh feed to show new activity
      await refreshFeed();

      console.log('✅ Community Feed Hook: Activity added successfully');
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to add activity');
      setError(error);
      console.error('❌ Community Feed Hook: Error adding activity:', error);
      throw error;
    }
  }, [refreshFeed]);

  // Initial fetch
  useEffect(() => {
    if (enabled) {
      Promise.all([fetchFeed(true), fetchTrending()]);
    }
  }, [enabled, fetchFeed, fetchTrending]);

  // Auto-refresh interval
  useEffect(() => {
    if (!enabled || !autoRefresh) return;

    const interval = setInterval(() => {
      handleRealtimeUpdate();
    }, 30000); // Check for updates every 30 seconds

    return () => clearInterval(interval);
  }, [enabled, autoRefresh, handleRealtimeUpdate]);

  return {
    activities,
    trendingActivities,
    isLoading,
    isLoadingMore,
    error,
    hasMore,
    totalCount,
    refreshFeed,
    loadMore,
    addActivity,
    newActivitiesCount,
    loadNewActivities
  };
}

/**
 * Hook for user-specific activity feed
 */
export function useUserActivityFeed(
  userId: string,
  options: Omit<UseCommunityActivityFeedOptions, 'user_id'> = {}
) {
  return useCommunityActivityFeed({
    ...options,
    user_id: userId
  });
}

/**
 * Hook for activity feed by type
 */
export function useActivityFeedByType(
  activityTypes: CommunityActivity['activity_type'][],
  options: Omit<UseCommunityActivityFeedOptions, 'activity_types'> = {}
) {
  return useCommunityActivityFeed({
    ...options,
    activity_types: activityTypes
  });
}

/**
 * Hook for content-specific activity feed
 */
export function useContentActivityFeed(
  contentTypes: CommunityActivity['content_type'][],
  options: Omit<UseCommunityActivityFeedOptions, 'content_types'> = {}
) {
  return useCommunityActivityFeed({
    ...options,
    content_types: contentTypes
  });
}

/**
 * Hook for trending activities only
 */
export function useTrendingActivities(timeframe: '1h' | '24h' | '7d' = '24h') {
  const [trending, setTrending] = useState<CommunityActivity[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchTrending = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const activities = await communityActivityFeedService.getTrendingActivities(timeframe);
      setTrending(activities);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch trending activities');
      setError(error);
    } finally {
      setIsLoading(false);
    }
  }, [timeframe]);

  useEffect(() => {
    fetchTrending();
  }, [fetchTrending]);

  return {
    trending,
    isLoading,
    error,
    refresh: fetchTrending
  };
}

/**
 * Hook for activity feed with infinite scroll
 */
export function useInfiniteActivityFeed(
  options: UseCommunityActivityFeedOptions = {}
) {
  const result = useCommunityActivityFeed({
    ...options,
    infiniteScroll: true
  });

  // Intersection observer for infinite scroll
  const [loadMoreRef, setLoadMoreRef] = useState<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!loadMoreRef || !result.hasMore || result.isLoadingMore) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          result.loadMore();
        }
      },
      { threshold: 0.1 }
    );

    observer.observe(loadMoreRef);

    return () => observer.disconnect();
  }, [loadMoreRef, result.hasMore, result.isLoadingMore, result.loadMore]);

  return {
    ...result,
    loadMoreRef: setLoadMoreRef
  };
}

export default useCommunityActivityFeed;
