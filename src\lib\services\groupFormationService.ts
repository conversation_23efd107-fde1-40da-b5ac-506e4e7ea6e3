/**
 * Group Formation Service
 *
 * Comprehensive service for smart group formation, buddy matching,
 * and real-time group suggestions for festival attendees.
 */

import { supabase } from '@/lib/supabase';

export interface GroupSuggestion {
  id: string;
  suggested_name: string;
  suggested_description: string;
  formation_type: 'buddy_match' | 'activity_group' | 'music_group' | 'festival_crew';
  festival_id?: string;
  activity_focus: string[];
  music_focus: string[];
  target_users: string[];
  creator_id: string;
  min_members: number;
  max_members: number;
  status: 'pending' | 'active' | 'completed' | 'expired';
  confidence_score: number;
  expires_at: string;
  created_at: string;
  updated_at: string;
}

export interface GroupMember {
  id: string;
  group_id: string;
  user_id: string;
  role: 'creator' | 'admin' | 'member';
  joined_at: string;
  left_at?: string;
  invited_by?: string;
}

export interface GroupFormationRequest {
  formation_type: GroupSuggestion['formation_type'];
  festival_id?: string;
  activity_focus?: string[];
  music_focus?: string[];
  min_members?: number;
  max_members?: number;
  description?: string;
}

export interface UserProfile {
  id: string;
  interests: string[];
  music_preferences: string[];
  festival_experience: string;
  group_preferences: {
    size: 'small' | 'medium' | 'large';
    activity_level: 'low' | 'medium' | 'high';
    social_style: 'introvert' | 'extrovert' | 'ambivert';
  };
}

class GroupFormationService {
  private readonly SUGGESTION_CACHE = new Map<string, GroupSuggestion[]>();
  private readonly CACHE_DURATION = 2 * 60 * 1000; // 2 minutes

  /**
   * Create a new group suggestion
   */
  async createGroupSuggestion(
    request: GroupFormationRequest,
    userId: string
  ): Promise<GroupSuggestion> {
    try {
      console.log('👥 Group Formation: Creating suggestion for user', userId);

      // Generate intelligent group name and description
      const { name, description } = this.generateGroupDetails(request);

      const suggestionData = {
        suggested_name: name,
        suggested_description: description,
        formation_type: request.formation_type,
        festival_id: request.festival_id || null,
        activity_focus: request.activity_focus || [],
        music_focus: request.music_focus || [],
        target_users: [], // Will be populated by matching algorithm
        creator_id: userId,
        min_members: request.min_members || 2,
        max_members: request.max_members || 8,
        status: 'pending' as const,
        confidence_score: 0.0,
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data, error } = await (supabase as any)
        .from('group_suggestions')
        .insert([suggestionData])
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Group Formation: Suggestion created', data.id);

      // Start matching process
      await this.findPotentialMatches(data.id);

      return data;
    } catch (error) {
      console.error('❌ Group Formation: Error creating suggestion:', error);
      throw error;
    }
  }

  /**
   * Get group suggestions for a user
   */
  async getGroupSuggestions(
    userId: string,
    filters: {
      formation_type?: GroupSuggestion['formation_type'];
      festival_id?: string;
      status?: GroupSuggestion['status'];
    } = {}
  ): Promise<GroupSuggestion[]> {
    try {
      const cacheKey = `suggestions_${userId}_${JSON.stringify(filters)}`;
      const cached = this.getCachedSuggestions(cacheKey);
      
      if (cached) {
        console.log('👥 Group Formation: Using cached suggestions');
        return cached;
      }

      let query = supabase
        .from('group_suggestions')
        .select('*');

      // Apply filters
      if (filters.formation_type) {
        query = query.eq('formation_type', filters.formation_type);
      }
      
      if (filters.festival_id) {
        query = query.eq('festival_id', filters.festival_id);
      }
      
      if (filters.status) {
        query = query.eq('status', filters.status);
      } else {
        // Default to active suggestions
        query = query.in('status', ['pending', 'active']);
      }

      // Only show non-expired suggestions
      query = query.gt('expires_at', new Date().toISOString());

      // Order by confidence score and creation date
      query = query
        .order('confidence_score', { ascending: false })
        .order('created_at', { ascending: false });

      const { data, error } = await query.limit(20);

      if (error) throw error;

      const suggestions = data || [];
      
      // Filter suggestions relevant to the user
      const relevantSuggestions = await this.filterRelevantSuggestions(suggestions, userId);
      
      // Cache the results
      this.setCachedSuggestions(cacheKey, relevantSuggestions);

      console.log('✅ Group Formation: Fetched', relevantSuggestions.length, 'suggestions');
      return relevantSuggestions;
    } catch (error) {
      console.error('❌ Group Formation: Error fetching suggestions:', error);
      return [];
    }
  }

  /**
   * Join a group suggestion
   */
  async joinGroup(suggestionId: string, userId: string): Promise<GroupMember> {
    try {
      console.log('👥 Group Formation: User joining group', suggestionId);

      // Check if user is already a member
      const { data: existingMember } = await supabase
        .from('group_members')
        .select('*')
        .eq('group_id', suggestionId)
        .eq('user_id', userId)
        .is('left_at', null)
        .single();

      if (existingMember) {
        throw new Error('User is already a member of this group');
      }

      // Check group capacity
      const { data: suggestion } = await supabase
        .from('group_suggestions')
        .select('max_members')
        .eq('id', suggestionId)
        .single();

      if (!suggestion) {
        throw new Error('Group suggestion not found');
      }

      const { count: currentMembers } = await supabase
        .from('group_members')
        .select('*', { count: 'exact' })
        .eq('group_id', suggestionId)
        .is('left_at', null);

      if (currentMembers && currentMembers >= suggestion.max_members) {
        throw new Error('Group is at maximum capacity');
      }

      // Add user to group
      const memberData = {
        group_id: suggestionId,
        user_id: userId,
        role: 'member' as const,
        joined_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('group_members')
        .insert([memberData])
        .select()
        .single();

      if (error) throw error;

      // Update group status if needed
      await this.updateGroupStatus(suggestionId);

      console.log('✅ Group Formation: User joined group successfully');
      return data;
    } catch (error) {
      console.error('❌ Group Formation: Error joining group:', error);
      throw error;
    }
  }

  /**
   * Leave a group
   */
  async leaveGroup(suggestionId: string, userId: string): Promise<void> {
    try {
      console.log('👥 Group Formation: User leaving group', suggestionId);

      const { error } = await supabase
        .from('group_members')
        .update({ left_at: new Date().toISOString() })
        .eq('group_id', suggestionId)
        .eq('user_id', userId)
        .is('left_at', null);

      if (error) throw error;

      // Update group status
      await this.updateGroupStatus(suggestionId);

      console.log('✅ Group Formation: User left group successfully');
    } catch (error) {
      console.error('❌ Group Formation: Error leaving group:', error);
      throw error;
    }
  }

  /**
   * Get group members
   */
  async getGroupMembers(suggestionId: string): Promise<GroupMember[]> {
    try {
      const { data, error } = await supabase
        .from('group_members')
        .select('*')
        .eq('group_id', suggestionId)
        .is('left_at', null)
        .order('joined_at', { ascending: true });

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('❌ Group Formation: Error fetching members:', error);
      return [];
    }
  }

  /**
   * Find potential matches for a group suggestion
   */
  private async findPotentialMatches(suggestionId: string): Promise<void> {
    try {
      // Get the suggestion details
      const { data: suggestion } = await supabase
        .from('group_suggestions')
        .select('*')
        .eq('id', suggestionId)
        .single();

      if (!suggestion) return;

      // Simple matching algorithm based on interests
      // In a real implementation, this would be more sophisticated
      const potentialUsers = await this.findUsersWithSimilarInterests(suggestion);
      
      // Update the suggestion with target users and confidence score
      const confidence = this.calculateConfidenceScore(suggestion, potentialUsers);
      
      await supabase
        .from('group_suggestions')
        .update({
          target_users: potentialUsers.slice(0, 10), // Top 10 matches
          confidence_score: confidence,
          status: potentialUsers.length > 0 ? 'active' : 'pending',
          updated_at: new Date().toISOString()
        })
        .eq('id', suggestionId);

      console.log('✅ Group Formation: Found', potentialUsers.length, 'potential matches');
    } catch (error) {
      console.error('❌ Group Formation: Error finding matches:', error);
    }
  }

  /**
   * Find users with similar interests (simplified algorithm)
   */
  private async findUsersWithSimilarInterests(suggestion: GroupSuggestion): Promise<string[]> {
    // This is a simplified implementation
    // In a real system, this would analyze user profiles, activity history, etc.
    
    try {
      // Get users who have similar activity or music interests
      const { data: users } = await supabase
        .from('user_activities')
        .select('user_id')
        .in('content_type', ['activity', 'festival'])
        .limit(50);

      if (!users) return [];

      // Return unique user IDs (excluding the creator)
      const uniqueUsers = [...new Set(users.map(u => u.user_id))]
        .filter(id => id !== suggestion.creator_id);

      return uniqueUsers.slice(0, 20);
    } catch (error) {
      console.error('❌ Group Formation: Error finding similar users:', error);
      return [];
    }
  }

  /**
   * Calculate confidence score for a group suggestion
   */
  private calculateConfidenceScore(suggestion: GroupSuggestion, potentialUsers: string[]): number {
    let score = 0;

    // Base score for having potential matches
    if (potentialUsers.length > 0) {
      score += 30;
    }

    // Score based on number of potential matches
    score += Math.min(potentialUsers.length * 5, 40);

    // Score based on specificity of interests
    if (suggestion.activity_focus.length > 0) {
      score += 10;
    }
    
    if (suggestion.music_focus.length > 0) {
      score += 10;
    }

    // Score based on reasonable group size
    const sizeRange = suggestion.max_members - suggestion.min_members;
    if (sizeRange >= 2 && sizeRange <= 6) {
      score += 10;
    }

    return Math.min(score, 100);
  }

  /**
   * Generate intelligent group name and description
   */
  private generateGroupDetails(request: GroupFormationRequest): { name: string; description: string } {
    const { formation_type, activity_focus, music_focus } = request;

    let name = '';
    let description = '';

    switch (formation_type) {
      case 'buddy_match':
        name = 'Festival Buddy Match';
        description = 'Looking for a festival buddy to explore together and share the experience!';
        break;
      
      case 'activity_group':
        const activity = activity_focus?.[0] || 'activities';
        name = `${activity.charAt(0).toUpperCase() + activity.slice(1)} Crew`;
        description = `Group focused on ${activity_focus?.join(', ') || 'various activities'} at the festival.`;
        break;
      
      case 'music_group':
        const genre = music_focus?.[0] || 'music';
        name = `${genre.charAt(0).toUpperCase() + genre.slice(1)} Lovers`;
        description = `Music enthusiasts who love ${music_focus?.join(', ') || 'great music'}!`;
        break;
      
      case 'festival_crew':
        name = 'Festival Squad';
        description = 'Complete festival crew for the ultimate festival experience together!';
        break;
      
      default:
        name = 'Festival Group';
        description = 'Join our festival group for an amazing shared experience!';
    }

    return { name, description };
  }

  /**
   * Filter suggestions relevant to a specific user
   */
  private async filterRelevantSuggestions(
    suggestions: GroupSuggestion[],
    userId: string
  ): Promise<GroupSuggestion[]> {
    // Filter out suggestions where user is already a member
    const userGroups = await this.getUserGroups(userId);
    const userGroupIds = new Set(userGroups.map(g => g.group_id));

    return suggestions.filter(suggestion => 
      !userGroupIds.has(suggestion.id) && 
      suggestion.creator_id !== userId
    );
  }

  /**
   * Get user's current groups
   */
  async getUserGroups(userId: string): Promise<GroupMember[]> {
    try {
      const { data, error } = await supabase
        .from('group_members')
        .select('*')
        .eq('user_id', userId)
        .is('left_at', null);

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('❌ Group Formation: Error fetching user groups:', error);
      return [];
    }
  }

  /**
   * Update group status based on current membership
   */
  private async updateGroupStatus(suggestionId: string): Promise<void> {
    try {
      const members = await this.getGroupMembers(suggestionId);
      const { data: suggestion } = await supabase
        .from('group_suggestions')
        .select('min_members, max_members')
        .eq('id', suggestionId)
        .single();

      if (!suggestion) return;

      let newStatus: GroupSuggestion['status'] = 'pending';
      
      if (members.length >= suggestion.min_members) {
        newStatus = 'active';
      }
      
      if (members.length >= suggestion.max_members) {
        newStatus = 'completed';
      }

      await supabase
        .from('group_suggestions')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', suggestionId);
    } catch (error) {
      console.error('❌ Group Formation: Error updating status:', error);
    }
  }

  /**
   * Cache management
   */
  private getCachedSuggestions(key: string): GroupSuggestion[] | null {
    const cached = this.SUGGESTION_CACHE.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }
    return null;
  }

  private setCachedSuggestions(key: string, data: GroupSuggestion[]): void {
    this.SUGGESTION_CACHE.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.SUGGESTION_CACHE.clear();
    console.log('👥 Group Formation: Cache cleared');
  }
}

// Export singleton instance
export const groupFormationService = new GroupFormationService();
export default groupFormationService;
