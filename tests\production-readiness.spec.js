/**
 * Production Readiness End-to-End Tests
 *
 * Comprehensive testing suite for validating production-ready functionality
 * including performance optimizations, real-time subscriptions, and error handling.
 */

import { test, expect, Page } from '@playwright/test'

// Test configuration
const BASE_URL = 'http://localhost:5175'
const TEST_TIMEOUT = 30000

// Helper functions
async function waitForPageLoad(page: Page) {
  await page.waitForLoadState('networkidle')
  await page.waitForTimeout(1000) // Additional buffer for React hydration
}

async function checkPerformanceMetrics(page: Page) {
  const performanceMetrics = await page.evaluate(() => {
    return {
      loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
      domContentLoaded: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
      firstPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime || 0,
      firstContentfulPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-contentful-paint')?.startTime || 0
    }
  })
  
  return performanceMetrics
}

test.describe('Production Readiness Validation', () => {
  test.beforeEach(async ({ page }) => {
    // Set up console logging for debugging
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.error('Browser console error:', msg.text())
      }
    })
    
    // Set up network monitoring
    page.on('response', response => {
      if (!response.ok() && response.status() >= 400) {
        console.error(`Network error: ${response.status()} ${response.url()}`)
      }
    })
  })

  test('Home page loads successfully with SmartHome component', async ({ page }) => {
    await page.goto(BASE_URL)
    await waitForPageLoad(page)
    
    // Verify page loads without errors
    await expect(page).toHaveTitle(/Festival Family/)
    
    // Check for critical elements
    const bodyContent = await page.textContent('body')
    expect(bodyContent).toBeTruthy()
    
    // Verify no JavaScript errors
    const errors = await page.evaluate(() => window.errors || [])
    expect(errors).toHaveLength(0)
    
    console.log('✅ Home page loads successfully')
  })

  test('Activities page performance and functionality', async ({ page }) => {
    await page.goto(`${BASE_URL}/activities`)
    await waitForPageLoad(page)
    
    // Check performance metrics
    const metrics = await checkPerformanceMetrics(page)
    expect(metrics.loadTime).toBeLessThan(3000) // Less than 3 seconds
    expect(metrics.firstContentfulPaint).toBeLessThan(1500) // Less than 1.5 seconds
    
    // Verify Activities page elements
    await expect(page.locator('h1')).toContainText('Activities')
    
    // Check for activity cards
    const activityCards = page.locator('[data-testid="activity-card"], .activity-card, .bento-card')
    await expect(activityCards.first()).toBeVisible({ timeout: 10000 })
    
    // Test tab functionality
    const tabs = page.locator('[role="tab"]')
    if (await tabs.count() > 0) {
      await tabs.first().click()
      await page.waitForTimeout(500)
    }
    
    console.log('✅ Activities page performance validated:', metrics)
  })

  test('Real-time subscription functionality', async ({ page }) => {
    await page.goto(`${BASE_URL}/activities`)
    await waitForPageLoad(page)
    
    // Monitor console for real-time subscription messages
    const realtimeMessages: string[] = []
    page.on('console', msg => {
      const text = msg.text()
      if (text.includes('Optimized subscription') || text.includes('Real-time update')) {
        realtimeMessages.push(text)
      }
    })
    
    // Wait for subscriptions to be established
    await page.waitForTimeout(3000)
    
    // Verify subscription messages
    expect(realtimeMessages.length).toBeGreaterThan(0)
    
    const hasSubscriptionMessage = realtimeMessages.some(msg => 
      msg.includes('Optimized subscription active') || 
      msg.includes('subscription created')
    )
    expect(hasSubscriptionMessage).toBeTruthy()
    
    console.log('✅ Real-time subscriptions established:', realtimeMessages.length, 'messages')
  })

  test('Error boundary functionality', async ({ page }) => {
    await page.goto(BASE_URL)
    await waitForPageLoad(page)
    
    // Test error boundary by triggering a JavaScript error
    await page.evaluate(() => {
      // Simulate a component error
      const errorEvent = new Error('Test error for error boundary')
      window.dispatchEvent(new ErrorEvent('error', { error: errorEvent }))
    })
    
    await page.waitForTimeout(1000)
    
    // Check if error boundary is working (page should still be functional)
    const bodyContent = await page.textContent('body')
    expect(bodyContent).toBeTruthy()
    
    console.log('✅ Error boundary handling verified')
  })

  test('Caching and performance optimization', async ({ page }) => {
    // First visit to populate cache
    await page.goto(`${BASE_URL}/activities`)
    await waitForPageLoad(page)
    const firstLoadTime = await checkPerformanceMetrics(page)
    
    // Second visit should be faster due to caching
    await page.reload()
    await waitForPageLoad(page)
    const secondLoadTime = await checkPerformanceMetrics(page)
    
    // Verify caching is working (second load should be faster or similar)
    expect(secondLoadTime.loadTime).toBeLessThanOrEqual(firstLoadTime.loadTime * 1.2) // Allow 20% variance
    
    console.log('✅ Caching optimization verified:', {
      firstLoad: firstLoadTime.loadTime,
      secondLoad: secondLoadTime.loadTime
    })
  })

  test('Mobile responsiveness', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto(`${BASE_URL}/activities`)
    await waitForPageLoad(page)
    
    // Verify mobile layout
    const bodyWidth = await page.evaluate(() => document.body.offsetWidth)
    expect(bodyWidth).toBeLessThanOrEqual(375)
    
    // Check for mobile-specific elements
    const mobileElements = page.locator('.mobile, [data-mobile="true"]')
    // Mobile elements may or may not exist, just verify page loads
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.reload()
    await waitForPageLoad(page)
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 })
    await page.reload()
    await waitForPageLoad(page)
    
    console.log('✅ Mobile responsiveness verified')
  })

  test('Navigation and routing', async ({ page }) => {
    await page.goto(BASE_URL)
    await waitForPageLoad(page)
    
    // Test navigation to Activities
    await page.goto(`${BASE_URL}/activities`)
    await waitForPageLoad(page)
    await expect(page).toHaveURL(/activities/)
    
    // Test navigation to Discover
    await page.goto(`${BASE_URL}/discover`)
    await waitForPageLoad(page)
    await expect(page).toHaveURL(/discover/)
    
    // Test back navigation
    await page.goBack()
    await waitForPageLoad(page)
    
    console.log('✅ Navigation and routing verified')
  })

  test('TypeScript compliance and build integrity', async ({ page }) => {
    // This test verifies that the application loads without TypeScript errors
    await page.goto(BASE_URL)
    await waitForPageLoad(page)
    
    // Check for TypeScript compilation errors in console
    const errors = await page.evaluate(() => {
      return Array.from(document.querySelectorAll('*')).filter(el => 
        el.textContent?.includes('TypeScript') || 
        el.textContent?.includes('compilation error')
      ).length
    })
    
    expect(errors).toBe(0)
    
    // Verify critical components are rendered
    const hasContent = await page.evaluate(() => {
      return document.body.children.length > 0 && 
             document.body.textContent && 
             document.body.textContent.trim().length > 0
    })
    
    expect(hasContent).toBeTruthy()
    
    console.log('✅ TypeScript compliance verified')
  })

  test('Database integration and data loading', async ({ page }) => {
    await page.goto(`${BASE_URL}/activities`)
    await waitForPageLoad(page)
    
    // Monitor network requests for database calls
    const apiRequests: string[] = []
    page.on('response', response => {
      if (response.url().includes('supabase') || response.url().includes('api')) {
        apiRequests.push(`${response.status()} ${response.url()}`)
      }
    })
    
    // Wait for data to load
    await page.waitForTimeout(5000)
    
    // Verify data is loaded (check for content)
    const hasActivities = await page.evaluate(() => {
      const content = document.body.textContent || ''
      return content.length > 1000 // Reasonable amount of content
    })
    
    expect(hasActivities).toBeTruthy()
    
    console.log('✅ Database integration verified:', apiRequests.length, 'API requests')
  })

  test('Performance under load simulation', async ({ page }) => {
    const loadTimes: number[] = []
    
    // Simulate multiple page loads
    for (let i = 0; i < 3; i++) {
      const startTime = Date.now()
      await page.goto(`${BASE_URL}/activities`)
      await waitForPageLoad(page)
      const endTime = Date.now()
      
      loadTimes.push(endTime - startTime)
      
      // Brief pause between loads
      await page.waitForTimeout(1000)
    }
    
    // Verify consistent performance
    const averageLoadTime = loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length
    const maxLoadTime = Math.max(...loadTimes)
    
    expect(averageLoadTime).toBeLessThan(5000) // Average under 5 seconds
    expect(maxLoadTime).toBeLessThan(8000) // Max under 8 seconds
    
    console.log('✅ Performance under load verified:', {
      average: averageLoadTime,
      max: maxLoadTime,
      all: loadTimes
    })
  })
})

test.describe('Critical User Flows', () => {
  test('Complete user journey through main features', async ({ page }) => {
    // Start at home
    await page.goto(BASE_URL)
    await waitForPageLoad(page)
    
    // Navigate to Activities
    await page.goto(`${BASE_URL}/activities`)
    await waitForPageLoad(page)
    
    // Interact with activities (if any exist)
    const activityCards = page.locator('[data-testid="activity-card"], .activity-card, .bento-card')
    if (await activityCards.count() > 0) {
      await activityCards.first().click()
      await page.waitForTimeout(1000)
    }
    
    // Navigate to Discover
    await page.goto(`${BASE_URL}/discover`)
    await waitForPageLoad(page)
    
    // Return to home
    await page.goto(BASE_URL)
    await waitForPageLoad(page)
    
    console.log('✅ Complete user journey verified')
  })
})
